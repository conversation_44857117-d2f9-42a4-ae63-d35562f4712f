
默认	23:13:50.235960+0800	FitCount	ℹ️ [SitUpCounter.swift:195] calculateTorsoAngle(from:deviceOrientation:): worldLandmarks 数组长度: 33
默认	23:13:50.236085+0800	FitCount	ℹ️ [SitUpCounter.swift:199] calculateTorsoAngle(from:deviceOrientation:): ┌─────────┬──────────┬──────────┬──────────┐
默认	23:13:50.236136+0800	FitCount	ℹ️ [SitUpCounter.swift:200] calculateTorsoAngle(from:deviceOrientation:): │  Index  │    X     │    Y     │    Z     │
默认	23:13:50.236011+0800	FitCount	ℹ️ [SitUpCounter.swift:196] calculateTorsoAngle(from:deviceOrientation:):
默认	23:13:50.236062+0800	FitCount	ℹ️ [SitUpCounter.swift:199] calculateTorsoAngle(from:deviceOrientation:): ┌─────────┬──────────┬──────────┬──────────┐
默认	23:13:50.236186+0800	FitCount	ℹ️ [SitUpCounter.swift:201] calculateTorsoAngle(from:deviceOrientation:): ├─────────┼──────────┼──────────┼──────────┤
默认	23:13:50.236113+0800	FitCount	ℹ️ [SitUpCounter.swift:200] calculateTorsoAngle(from:deviceOrientation:): │  Index  │    X     │    Y     │    Z     │
默认	23:13:50.236241+0800	FitCount	ℹ️ [SitUpCounter.swift:210] calculateTorsoAngle(from:deviceOrientation:): │     0   │   -0.179 │    0.608 │   -0.014 │
默认	23:13:50.236290+0800	FitCount	ℹ️ [SitUpCounter.swift:210] calculateTorsoAngle(from:deviceOrientation:): │     1   │   -0.165 │    0.650 │   -0.010 │
默认	23:13:50.236162+0800	FitCount	ℹ️ [SitUpCounter.swift:201] calculateTorsoAngle(from:deviceOrientation:): ├─────────┼──────────┼──────────┼──────────┤
默认	23:13:50.236340+0800	FitCount	ℹ️ [SitUpCounter.swift:210] calculateTorsoAngle(from:deviceOrientation:): │     2   │   -0.164 │    0.650 │   -0.012 │
默认	23:13:50.236217+0800	FitCount	ℹ️ [SitUpCounter.swift:210] calculateTorsoAngle(from:deviceOrientation:): │     0   │   -0.179 │    0.608 │   -0.014 │
默认	23:13:50.236267+0800	FitCount	ℹ️ [SitUpCounter.swift:210] calculateTorsoAngle(from:deviceOrientation:): │     1   │   -0.165 │    0.650 │   -0.010 │
默认	23:13:50.236390+0800	FitCount	ℹ️ [SitUpCounter.swift:210] calculateTorsoAngle(from:deviceOrientation:): │     3   │   -0.165 │    0.651 │   -0.010 │
默认	23:13:50.236316+0800	FitCount	ℹ️ [SitUpCounter.swift:210] calculateTorsoAngle(from:deviceOrientation:): │     2   │   -0.164 │    0.650 │   -0.012 │
默认	23:13:50.236439+0800	FitCount	ℹ️ [SitUpCounter.swift:210] calculateTorsoAngle(from:deviceOrientation:): │     4   │   -0.160 │    0.646 │   -0.046 │
默认	23:13:50.236489+0800	FitCount	ℹ️ [SitUpCounter.swift:210] calculateTorsoAngle(from:deviceOrientation:): │     5   │   -0.162 │    0.647 │   -0.046 │
默认	23:13:50.236366+0800	FitCount	ℹ️ [SitUpCounter.swift:210] calculateTorsoAngle(from:deviceOrientation:): │     3   │   -0.165 │    0.651 │   -0.010 │
默认	23:13:50.236537+0800	FitCount	ℹ️ [SitUpCounter.swift:210] calculateTorsoAngle(from:deviceOrientation:): │     6   │   -0.162 │    0.646 │   -0.042 │
默认	23:13:50.236416+0800	FitCount	ℹ️ [SitUpCounter.swift:210] calculateTorsoAngle(from:deviceOrientation:): │     4   │   -0.160 │    0.646 │   -0.046 │
默认	23:13:50.236464+0800	FitCount	ℹ️ [SitUpCounter.swift:210] calculateTorsoAngle(from:deviceOrientation:): │     5   │   -0.162 │    0.647 │   -0.046 │
默认	23:13:50.236587+0800	FitCount	ℹ️ [SitUpCounter.swift:210] calculateTorsoAngle(from:deviceOrientation:): │     7   │   -0.069 │    0.663 │    0.052 │
默认	23:13:50.236514+0800	FitCount	ℹ️ [SitUpCounter.swift:210] calculateTorsoAngle(from:deviceOrientation:): │     6   │   -0.162 │    0.646 │   -0.042 │
默认	23:13:50.236635+0800	FitCount	ℹ️ [SitUpCounter.swift:210] calculateTorsoAngle(from:deviceOrientation:): │     8   │   -0.058 │    0.638 │   -0.094 │
默认	23:13:50.236562+0800	FitCount	ℹ️ [SitUpCounter.swift:210] calculateTorsoAngle(from:deviceOrientation:): │     7   │   -0.069 │    0.663 │    0.052 │
默认	23:13:50.236685+0800	FitCount	ℹ️ [SitUpCounter.swift:210] calculateTorsoAngle(from:deviceOrientation:): │     9   │   -0.135 │    0.590 │    0.006 │
默认	23:13:50.236613+0800	FitCount	ℹ️ [SitUpCounter.swift:210] calculateTorsoAngle(from:deviceOrientation:): │     8   │   -0.058 │    0.638 │   -0.094 │
默认	23:13:50.236735+0800	FitCount	ℹ️ [SitUpCounter.swift:210] calculateTorsoAngle(from:deviceOrientation:): │    10   │   -0.133 │    0.582 │   -0.035 │
默认	23:13:50.236661+0800	FitCount	ℹ️ [SitUpCounter.swift:210] calculateTorsoAngle(from:deviceOrientation:): │     9   │   -0.135 │    0.590 │    0.006 │
默认	23:13:50.236783+0800	FitCount	ℹ️ [SitUpCounter.swift:210] calculateTorsoAngle(from:deviceOrientation:): │    11   │   -0.054 │    0.513 │    0.155 │
默认	23:13:50.236711+0800	FitCount	ℹ️ [SitUpCounter.swift:210] calculateTorsoAngle(from:deviceOrientation:): │    10   │   -0.133 │    0.582 │   -0.035 │
默认	23:13:50.236833+0800	FitCount	ℹ️ [SitUpCounter.swift:210] calculateTorsoAngle(from:deviceOrientation:): │    12   │   -0.007 │    0.498 │   -0.160 │
默认	23:13:50.236759+0800	FitCount	ℹ️ [SitUpCounter.swift:210] calculateTorsoAngle(from:deviceOrientation:): │    11   │   -0.054 │    0.513 │    0.155 │
默认	23:13:50.236881+0800	FitCount	ℹ️ [SitUpCounter.swift:210] calculateTorsoAngle(from:deviceOrientation:): │    13   │   -0.235 │    0.548 │    0.272 │
默认	23:13:50.236931+0800	FitCount	ℹ️ [SitUpCounter.swift:210] calculateTorsoAngle(from:deviceOrientation:): │    14   │   -0.176 │    0.526 │   -0.179 │
默认	23:13:50.236809+0800	FitCount	ℹ️ [SitUpCounter.swift:210] calculateTorsoAngle(from:deviceOrientation:): │    12   │   -0.007 │    0.498 │   -0.160 │
默认	23:13:50.236980+0800	FitCount	ℹ️ [SitUpCounter.swift:210] calculateTorsoAngle(from:deviceOrientation:): │    15   │   -0.153 │    0.662 │    0.340 │
默认	23:13:50.236857+0800	FitCount	ℹ️ [SitUpCounter.swift:210] calculateTorsoAngle(from:deviceOrientation:): │    13   │   -0.235 │    0.548 │    0.272 │
默认	23:13:50.237030+0800	FitCount	ℹ️ [SitUpCounter.swift:210] calculateTorsoAngle(from:deviceOrientation:): │    16   │   -0.124 │    0.648 │   -0.050 │
默认	23:13:50.236907+0800	FitCount	ℹ️ [SitUpCounter.swift:210] calculateTorsoAngle(from:deviceOrientation:): │    14   │   -0.176 │    0.526 │   -0.179 │
默认	23:13:50.237080+0800	FitCount	ℹ️ [SitUpCounter.swift:210] calculateTorsoAngle(from:deviceOrientation:): │    17   │   -0.123 │    0.681 │    0.352 │
默认	23:13:50.236957+0800	FitCount	ℹ️ [SitUpCounter.swift:210] calculateTorsoAngle(from:deviceOrientation:): │    15   │   -0.153 │    0.662 │    0.340 │
默认	23:13:50.237128+0800	FitCount	ℹ️ [SitUpCounter.swift:210] calculateTorsoAngle(from:deviceOrientation:): │    18   │   -0.101 │    0.666 │   -0.062 │
默认	23:13:50.237006+0800	FitCount	ℹ️ [SitUpCounter.swift:210] calculateTorsoAngle(from:deviceOrientation:): │    16   │   -0.124 │    0.648 │   -0.050 │
默认	23:13:50.237056+0800	FitCount	ℹ️ [SitUpCounter.swift:210] calculateTorsoAngle(from:deviceOrientation:): │    17   │   -0.123 │    0.681 │    0.352 │
默认	23:13:50.237179+0800	FitCount	ℹ️ [SitUpCounter.swift:210] calculateTorsoAngle(from:deviceOrientation:): │    19   │   -0.096 │    0.691 │    0.352 │
默认	23:13:50.237104+0800	FitCount	ℹ️ [SitUpCounter.swift:210] calculateTorsoAngle(from:deviceOrientation:): │    18   │   -0.101 │    0.666 │   -0.062 │
默认	23:13:50.237277+0800	FitCount	ℹ️ [SitUpCounter.swift:210] calculateTorsoAngle(from:deviceOrientation:): │    20   │   -0.074 │    0.647 │   -0.058 │
默认	23:13:50.237154+0800	FitCount	ℹ️ [SitUpCounter.swift:210] calculateTorsoAngle(from:deviceOrientation:): │    19   │   -0.096 │    0.691 │    0.352 │
默认	23:13:50.237204+0800	FitCount	ℹ️ [SitUpCounter.swift:210] calculateTorsoAngle(from:deviceOrientation:): │    20   │   -0.074 │    0.647 │   -0.058 │
默认	23:13:50.237338+0800	FitCount	ℹ️ [SitUpCounter.swift:210] calculateTorsoAngle(from:deviceOrientation:): │    21   │   -0.131 │    0.670 │    0.338 │
默认	23:13:50.237389+0800	FitCount	ℹ️ [SitUpCounter.swift:210] calculateTorsoAngle(from:deviceOrientation:): │    22   │   -0.107 │    0.641 │   -0.047 │
默认	23:13:50.237440+0800	FitCount	ℹ️ [SitUpCounter.swift:210] calculateTorsoAngle(from:deviceOrientation:): │    23   │   -0.030 │    0.001 │    0.100 │
默认	23:13:50.237363+0800	FitCount	ℹ️ [SitUpCounter.swift:210] calculateTorsoAngle(from:deviceOrientation:): │    21   │   -0.131 │    0.670 │    0.338 │
默认	23:13:50.237489+0800	FitCount	ℹ️ [SitUpCounter.swift:210] calculateTorsoAngle(from:deviceOrientation:): │    24   │    0.030 │   -0.001 │   -0.099 │
默认	23:13:50.237413+0800	FitCount	ℹ️ [SitUpCounter.swift:210] calculateTorsoAngle(from:deviceOrientation:): │    22   │   -0.107 │    0.641 │   -0.047 │
默认	23:13:50.237538+0800	FitCount	ℹ️ [SitUpCounter.swift:210] calculateTorsoAngle(from:deviceOrientation:): │    25   │   -0.266 │   -0.265 │    0.102 │
默认	23:13:50.237464+0800	FitCount	ℹ️ [SitUpCounter.swift:210] calculateTorsoAngle(from:deviceOrientation:): │    23   │   -0.030 │    0.001 │    0.100 │
默认	23:13:50.237587+0800	FitCount	ℹ️ [SitUpCounter.swift:210] calculateTorsoAngle(from:deviceOrientation:): │    26   │   -0.256 │   -0.243 │   -0.136 │
默认	23:13:50.237514+0800	FitCount	ℹ️ [SitUpCounter.swift:210] calculateTorsoAngle(from:deviceOrientation:): │    24   │    0.030 │   -0.001 │   -0.099 │
默认	23:13:50.237638+0800	FitCount	ℹ️ [SitUpCounter.swift:210] calculateTorsoAngle(from:deviceOrientation:): │    27   │    0.039 │   -0.478 │    0.148 │
默认	23:13:50.237561+0800	FitCount	ℹ️ [SitUpCounter.swift:210] calculateTorsoAngle(from:deviceOrientation:): │    25   │   -0.266 │   -0.265 │    0.102 │
默认	23:13:50.237688+0800	FitCount	ℹ️ [SitUpCounter.swift:210] calculateTorsoAngle(from:deviceOrientation:): │    28   │    0.027 │   -0.491 │   -0.046 │
默认	23:13:50.237612+0800	FitCount	ℹ️ [SitUpCounter.swift:210] calculateTorsoAngle(from:deviceOrientation:): │    26   │   -0.256 │   -0.243 │   -0.136 │
默认	23:13:50.237663+0800	FitCount	ℹ️ [SitUpCounter.swift:210] calculateTorsoAngle(from:deviceOrientation:): │    27   │    0.039 │   -0.478 │    0.148 │
默认	23:13:50.237736+0800	FitCount	ℹ️ [SitUpCounter.swift:210] calculateTorsoAngle(from:deviceOrientation:): │    29   │    0.073 │   -0.505 │    0.151 │
默认	23:13:50.237793+0800	FitCount	ℹ️ [SitUpCounter.swift:210] calculateTorsoAngle(from:deviceOrientation:): │    30   │    0.061 │   -0.517 │   -0.036 │
默认	23:13:50.237711+0800	FitCount	ℹ️ [SitUpCounter.swift:210] calculateTorsoAngle(from:deviceOrientation:): │    28   │    0.027 │   -0.491 │   -0.046 │
默认	23:13:50.237843+0800	FitCount	ℹ️ [SitUpCounter.swift:210] calculateTorsoAngle(from:deviceOrientation:): │    31   │    0.071 │   -0.639 │    0.132 │
默认	23:13:50.237768+0800	FitCount	ℹ️ [SitUpCounter.swift:210] calculateTorsoAngle(from:deviceOrientation:): │    29   │    0.073 │   -0.505 │    0.151 │
默认	23:13:50.237891+0800	FitCount	ℹ️ [SitUpCounter.swift:210] calculateTorsoAngle(from:deviceOrientation:): │    32   │    0.078 │   -0.663 │   -0.036 │
默认	23:13:50.237818+0800	FitCount	ℹ️ [SitUpCounter.swift:210] calculateTorsoAngle(from:deviceOrientation:): │    30   │    0.061 │   -0.517 │   -0.036 │
默认	23:13:50.237941+0800	FitCount	ℹ️ [SitUpCounter.swift:214] calculateTorsoAngle(from:deviceOrientation:): └─────────┴──────────┴──────────┴──────────┘
默认	23:13:50.237867+0800	FitCount	ℹ️ [SitUpCounter.swift:210] calculateTorsoAngle(from:deviceOrientation:): │    31   │    0.071 │   -0.639 │    0.132 │
默认	23:13:50.238017+0800	FitCount	ℹ️ [SitUpCounter.swift:215] calculateTorsoAngle(from:deviceOrientation:):
默认	23:13:50.237915+0800	FitCount	ℹ️ [SitUpCounter.swift:210] calculateTorsoAngle(from:deviceOrientation:): │    32   │    0.078 │   -0.663 │   -0.036 │
默认	23:13:50.237991+0800	FitCount	ℹ️ [SitUpCounter.swift:214] calculateTorsoAngle(from:deviceOrientation:): └─────────┴──────────┴──────────┴──────────┘
默认	23:13:50.238073+0800	FitCount	ℹ️ [SitUpCounter.swift:216] calculateTorsoAngle(from:deviceOrientation:): worldLandmarks 数组信息打印完成
默认	23:13:50.238121+0800	FitCount	ℹ️ [SitUpCounter.swift:217] calculateTorsoAngle(from:deviceOrientation:):
默认	23:13:50.238044+0800	FitCount	ℹ️ [SitUpCounter.swift:215] calculateTorsoAngle(from:deviceOrientation:):
默认	23:13:50.238096+0800	FitCount	ℹ️ [SitUpCounter.swift:216] calculateTorsoAngle(from:deviceOrientation:): worldLandmarks 数组信息打印完成
默认	23:13:50.238143+0800	FitCount	ℹ️ [SitUpCounter.swift:217] calculateTorsoAngle(from:deviceOrientation:):
默认	23:13:50.238599+0800	FitCount	ℹ️ [SitUpCounter.swift:315] calculateTorsoAngle(from:deviceOrientation:): calculateTorsoAngle  躯干角度计算详情（2D简化版）:
默认	23:13:50.238645+0800	FitCount	ℹ️ [SitUpCounter.swift:316] calculateTorsoAngle(from:deviceOrientation:): calculateTorsoAngle  肩膀中点: (-0.031, 0.506)
默认	23:13:50.238689+0800	FitCount	ℹ️ [SitUpCounter.swift:317] calculateTorsoAngle(from:deviceOrientation:): calculateTorsoAngle  臀部中点: (0.000, 0.000)
默认	23:13:50.238620+0800	FitCount	ℹ️ [SitUpCounter.swift:315] calculateTorsoAngle(from:deviceOrientation:): calculateTorsoAngle  躯干角度计算详情（2D简化版）:
默认	23:13:50.238735+0800	FitCount	ℹ️ [SitUpCounter.swift:318] calculateTorsoAngle(from:deviceOrientation:): calculateTorsoAngle  躯干向量: (-0.061, 0.998)
默认	23:13:50.238667+0800	FitCount	ℹ️ [SitUpCounter.swift:316] calculateTorsoAngle(from:deviceOrientation:): calculateTorsoAngle  肩膀中点: (-0.031, 0.506)
默认	23:13:50.238780+0800	FitCount	ℹ️ [SitUpCounter.swift:319] calculateTorsoAngle(from:deviceOrientation:): calculateTorsoAngle  重力方向: (-0.031, 0.400)
默认	23:13:50.238713+0800	FitCount	ℹ️ [SitUpCounter.swift:317] calculateTorsoAngle(from:deviceOrientation:): calculateTorsoAngle  臀部中点: (0.000, 0.000)
默认	23:13:50.238758+0800	FitCount	ℹ️ [SitUpCounter.swift:318] calculateTorsoAngle(from:deviceOrientation:): calculateTorsoAngle  躯干向量: (-0.061, 0.998)
默认	23:13:50.238873+0800	FitCount	ℹ️ [SitUpCounter.swift:321] calculateTorsoAngle(from:deviceOrientation:): calculateTorsoAngle  最终角度: 66.3°
默认	23:13:50.238802+0800	FitCount	ℹ️ [SitUpCounter.swift:319] calculateTorsoAngle(from:deviceOrientation:): calculateTorsoAngle  重力方向: (-0.031, 0.400)
默认	23:13:50.238929+0800	FitCount	ℹ️ [SitUpCounter.swift:321] calculateTorsoAngle(from:deviceOrientation:): calculateTorsoAngle  最终角度: 66.3°



横屏


默认	23:17:00.199611+0800	FitCount	ℹ️ [SitUpCounter.swift:194] calculateTorsoAngle(from:deviceOrientation:): 开始打印 worldLandmarks 数组详细信息
默认	23:17:00.199863+0800	FitCount	ℹ️ [SitUpCounter.swift:195] calculateTorsoAngle(from:deviceOrientation:): worldLandmarks 数组长度: 33
默认	23:17:00.199753+0800	FitCount	ℹ️ [SitUpCounter.swift:194] calculateTorsoAngle(from:deviceOrientation:): 开始打印 worldLandmarks 数组详细信息
默认	23:17:00.200048+0800	FitCount	ℹ️ [SitUpCounter.swift:196] calculateTorsoAngle(from:deviceOrientation:):
默认	23:17:00.200227+0800	FitCount	ℹ️ [SitUpCounter.swift:199] calculateTorsoAngle(from:deviceOrientation:): ┌─────────┬──────────┬──────────┬──────────┐
默认	23:17:00.199958+0800	FitCount	ℹ️ [SitUpCounter.swift:195] calculateTorsoAngle(from:deviceOrientation:): worldLandmarks 数组长度: 33
默认	23:17:00.200403+0800	FitCount	ℹ️ [SitUpCounter.swift:200] calculateTorsoAngle(from:deviceOrientation:): │  Index  │    X     │    Y     │    Z     │
默认	23:17:00.200130+0800	FitCount	ℹ️ [SitUpCounter.swift:196] calculateTorsoAngle(from:deviceOrientation:):
默认	23:17:00.200572+0800	FitCount	ℹ️ [SitUpCounter.swift:201] calculateTorsoAngle(from:deviceOrientation:): ├─────────┼──────────┼──────────┼──────────┤
默认	23:17:00.200313+0800	FitCount	ℹ️ [SitUpCounter.swift:199] calculateTorsoAngle(from:deviceOrientation:): ┌─────────┬──────────┬──────────┬──────────┐
默认	23:17:00.200748+0800	FitCount	ℹ️ [SitUpCounter.swift:210] calculateTorsoAngle(from:deviceOrientation:): │     0   │   -0.615 │   -0.149 │   -0.020 │
默认	23:17:00.200485+0800	FitCount	ℹ️ [SitUpCounter.swift:200] calculateTorsoAngle(from:deviceOrientation:): │  Index  │    X     │    Y     │    Z     │
默认	23:17:00.200917+0800	FitCount	ℹ️ [SitUpCounter.swift:210] calculateTorsoAngle(from:deviceOrientation:): │     1   │   -0.657 │   -0.135 │   -0.016 │
默认	23:17:00.200653+0800	FitCount	ℹ️ [SitUpCounter.swift:201] calculateTorsoAngle(from:deviceOrientation:): ├─────────┼──────────┼──────────┼──────────┤
默认	23:17:00.201081+0800	FitCount	ℹ️ [SitUpCounter.swift:210] calculateTorsoAngle(from:deviceOrientation:): │     2   │   -0.657 │   -0.134 │   -0.018 │
默认	23:17:00.200831+0800	FitCount	ℹ️ [SitUpCounter.swift:210] calculateTorsoAngle(from:deviceOrientation:): │     0   │   -0.615 │   -0.149 │   -0.020 │
默认	23:17:00.201251+0800	FitCount	ℹ️ [SitUpCounter.swift:210] calculateTorsoAngle(from:deviceOrientation:): │     3   │   -0.657 │   -0.135 │   -0.016 │
默认	23:17:00.200998+0800	FitCount	ℹ️ [SitUpCounter.swift:210] calculateTorsoAngle(from:deviceOrientation:): │     1   │   -0.657 │   -0.135 │   -0.016 │
默认	23:17:00.201440+0800	FitCount	ℹ️ [SitUpCounter.swift:210] calculateTorsoAngle(from:deviceOrientation:): │     4   │   -0.652 │   -0.128 │   -0.052 │
默认	23:17:00.201168+0800	FitCount	ℹ️ [SitUpCounter.swift:210] calculateTorsoAngle(from:deviceOrientation:): │     2   │   -0.657 │   -0.134 │   -0.018 │
默认	23:17:00.201821+0800	FitCount	ℹ️ [SitUpCounter.swift:210] calculateTorsoAngle(from:deviceOrientation:): │     5   │   -0.653 │   -0.130 │   -0.052 │
默认	23:17:00.201333+0800	FitCount	ℹ️ [SitUpCounter.swift:210] calculateTorsoAngle(from:deviceOrientation:): │     3   │   -0.657 │   -0.135 │   -0.016 │
默认	23:17:00.201992+0800	FitCount	ℹ️ [SitUpCounter.swift:210] calculateTorsoAngle(from:deviceOrientation:): │     6   │   -0.652 │   -0.130 │   -0.048 │
默认	23:17:00.201582+0800	FitCount	ℹ️ [SitUpCounter.swift:210] calculateTorsoAngle(from:deviceOrientation:): │     4   │   -0.652 │   -0.128 │   -0.052 │
默认	23:17:00.202162+0800	FitCount	ℹ️ [SitUpCounter.swift:210] calculateTorsoAngle(from:deviceOrientation:): │     7   │   -0.667 │   -0.040 │    0.049 │
默认	23:17:00.201908+0800	FitCount	ℹ️ [SitUpCounter.swift:210] calculateTorsoAngle(from:deviceOrientation:): │     5   │   -0.653 │   -0.130 │   -0.052 │
默认	23:17:00.202388+0800	FitCount	ℹ️ [SitUpCounter.swift:210] calculateTorsoAngle(from:deviceOrientation:): │     8   │   -0.642 │   -0.023 │   -0.096 │
默认	23:17:00.202075+0800	FitCount	ℹ️ [SitUpCounter.swift:210] calculateTorsoAngle(from:deviceOrientation:): │     6   │   -0.652 │   -0.130 │   -0.048 │
默认	23:17:00.202559+0800	FitCount	ℹ️ [SitUpCounter.swift:210] calculateTorsoAngle(from:deviceOrientation:): │     9   │   -0.595 │   -0.106 │    0.001 │
默认	23:17:00.202281+0800	FitCount	ℹ️ [SitUpCounter.swift:210] calculateTorsoAngle(from:deviceOrientation:): │     7   │   -0.667 │   -0.040 │    0.049 │
默认	23:17:00.202729+0800	FitCount	ℹ️ [SitUpCounter.swift:210] calculateTorsoAngle(from:deviceOrientation:): │    10   │   -0.588 │   -0.102 │   -0.040 │
默认	23:17:00.202474+0800	FitCount	ℹ️ [SitUpCounter.swift:210] calculateTorsoAngle(from:deviceOrientation:): │     8   │   -0.642 │   -0.023 │   -0.096 │
默认	23:17:00.202889+0800	FitCount	ℹ️ [SitUpCounter.swift:210] calculateTorsoAngle(from:deviceOrientation:): │    11   │   -0.514 │   -0.035 │    0.156 │
默认	23:17:00.202641+0800	FitCount	ℹ️ [SitUpCounter.swift:210] calculateTorsoAngle(from:deviceOrientation:): │     9   │   -0.595 │   -0.106 │    0.001 │
默认	23:17:00.203051+0800	FitCount	ℹ️ [SitUpCounter.swift:210] calculateTorsoAngle(from:deviceOrientation:): │    12   │   -0.500 │    0.020 │   -0.161 │
默认	23:17:00.202807+0800	FitCount	ℹ️ [SitUpCounter.swift:210] calculateTorsoAngle(from:deviceOrientation:): │    10   │   -0.588 │   -0.102 │   -0.040 │
默认	23:17:00.202969+0800	FitCount	ℹ️ [SitUpCounter.swift:210] calculateTorsoAngle(from:deviceOrientation:): │    11   │   -0.514 │   -0.035 │    0.156 │
默认	23:17:00.203222+0800	FitCount	ℹ️ [SitUpCounter.swift:210] calculateTorsoAngle(from:deviceOrientation:): │    13   │   -0.546 │   -0.222 │    0.257 │
默认	23:17:00.203130+0800	FitCount	ℹ️ [SitUpCounter.swift:210] calculateTorsoAngle(from:deviceOrientation:): │    12   │   -0.500 │    0.020 │   -0.161 │
默认	23:17:00.203385+0800	FitCount	ℹ️ [SitUpCounter.swift:210] calculateTorsoAngle(from:deviceOrientation:): │    14   │   -0.534 │   -0.150 │   -0.179 │
默认	23:17:00.203303+0800	FitCount	ℹ️ [SitUpCounter.swift:210] calculateTorsoAngle(from:deviceOrientation:): │    13   │   -0.546 │   -0.222 │    0.257 │
默认	23:17:00.203543+0800	FitCount	ℹ️ [SitUpCounter.swift:210] calculateTorsoAngle(from:deviceOrientation:): │    15   │   -0.647 │   -0.129 │    0.298 │
默认	23:17:00.203463+0800	FitCount	ℹ️ [SitUpCounter.swift:210] calculateTorsoAngle(from:deviceOrientation:): │    14   │   -0.534 │   -0.150 │   -0.179 │
默认	23:17:00.203702+0800	FitCount	ℹ️ [SitUpCounter.swift:210] calculateTorsoAngle(from:deviceOrientation:): │    16   │   -0.657 │   -0.099 │   -0.041 │
默认	23:17:00.203622+0800	FitCount	ℹ️ [SitUpCounter.swift:210] calculateTorsoAngle(from:deviceOrientation:): │    15   │   -0.647 │   -0.129 │    0.298 │
默认	23:17:00.203862+0800	FitCount	ℹ️ [SitUpCounter.swift:210] calculateTorsoAngle(from:deviceOrientation:): │    17   │   -0.663 │   -0.097 │    0.307 │
默认	23:17:00.203780+0800	FitCount	ℹ️ [SitUpCounter.swift:210] calculateTorsoAngle(from:deviceOrientation:): │    16   │   -0.657 │   -0.099 │   -0.041 │
默认	23:17:00.203944+0800	FitCount	ℹ️ [SitUpCounter.swift:210] calculateTorsoAngle(from:deviceOrientation:): │    17   │   -0.663 │   -0.097 │    0.307 │
默认	23:17:00.204025+0800	FitCount	ℹ️ [SitUpCounter.swift:210] calculateTorsoAngle(from:deviceOrientation:): │    18   │   -0.675 │   -0.076 │   -0.052 │
默认	23:17:00.204000+0800	FitCount	ℹ️ [SitUpCounter.swift:210] calculateTorsoAngle(from:deviceOrientation:): │    18   │   -0.675 │   -0.076 │   -0.052 │
默认	23:17:00.204073+0800	FitCount	ℹ️ [SitUpCounter.swift:210] calculateTorsoAngle(from:deviceOrientation:): │    19   │   -0.671 │   -0.066 │    0.307 │
默认	23:17:00.204195+0800	FitCount	ℹ️ [SitUpCounter.swift:210] calculateTorsoAngle(from:deviceOrientation:): │    20   │   -0.654 │   -0.047 │   -0.048 │
默认	23:17:00.204051+0800	FitCount	ℹ️ [SitUpCounter.swift:210] calculateTorsoAngle(from:deviceOrientation:): │    19   │   -0.671 │   -0.066 │    0.307 │
默认	23:17:00.204101+0800	FitCount	ℹ️ [SitUpCounter.swift:210] calculateTorsoAngle(from:deviceOrientation:): │    20   │   -0.654 │   -0.047 │   -0.048 │
默认	23:17:00.204260+0800	FitCount	ℹ️ [SitUpCounter.swift:210] calculateTorsoAngle(from:deviceOrientation:): │    21   │   -0.654 │   -0.105 │    0.295 │
默认	23:17:00.204284+0800	FitCount	ℹ️ [SitUpCounter.swift:210] calculateTorsoAngle(from:deviceOrientation:): │    21   │   -0.654 │   -0.105 │    0.295 │
默认	23:17:00.204333+0800	FitCount	ℹ️ [SitUpCounter.swift:210] calculateTorsoAngle(from:deviceOrientation:): │    22   │   -0.649 │   -0.081 │   -0.037 │
默认	23:17:00.204308+0800	FitCount	ℹ️ [SitUpCounter.swift:210] calculateTorsoAngle(from:deviceOrientation:): │    22   │   -0.649 │   -0.081 │   -0.037 │
默认	23:17:00.204382+0800	FitCount	ℹ️ [SitUpCounter.swift:210] calculateTorsoAngle(from:deviceOrientation:): │    23   │   -0.001 │   -0.031 │    0.100 │
默认	23:17:00.204359+0800	FitCount	ℹ️ [SitUpCounter.swift:210] calculateTorsoAngle(from:deviceOrientation:): │    23   │   -0.001 │   -0.031 │    0.100 │
默认	23:17:00.204407+0800	FitCount	ℹ️ [SitUpCounter.swift:210] calculateTorsoAngle(from:deviceOrientation:): │    24   │    0.001 │    0.032 │   -0.100 │
默认	23:17:00.204432+0800	FitCount	ℹ️ [SitUpCounter.swift:210] calculateTorsoAngle(from:deviceOrientation:): │    24   │    0.001 │    0.032 │   -0.100 │
默认	23:17:00.204691+0800	FitCount	ℹ️ [SitUpCounter.swift:210] calculateTorsoAngle(from:deviceOrientation:): │    25   │    0.253 │   -0.271 │    0.102 │
默认	23:17:00.204540+0800	FitCount	ℹ️ [SitUpCounter.swift:210] calculateTorsoAngle(from:deviceOrientation:): │    25   │    0.253 │   -0.271 │    0.102 │
默认	23:17:00.204793+0800	FitCount	ℹ️ [SitUpCounter.swift:210] calculateTorsoAngle(from:deviceOrientation:): │    26   │    0.235 │   -0.251 │   -0.137 │
默认	23:17:00.204876+0800	FitCount	ℹ️ [SitUpCounter.swift:210] calculateTorsoAngle(from:deviceOrientation:): │    26   │    0.235 │   -0.251 │   -0.137 │
默认	23:17:00.204960+0800	FitCount	ℹ️ [SitUpCounter.swift:210] calculateTorsoAngle(from:deviceOrientation:): │    27   │    0.483 │    0.030 │    0.148 │
默认	23:17:00.205046+0800	FitCount	ℹ️ [SitUpCounter.swift:210] calculateTorsoAngle(from:deviceOrientation:): │    27   │    0.483 │    0.030 │    0.148 │
默认	23:17:00.205250+0800	FitCount	ℹ️ [SitUpCounter.swift:210] calculateTorsoAngle(from:deviceOrientation:): │    28   │    0.493 │    0.019 │   -0.048 │
默认	23:17:00.205128+0800	FitCount	ℹ️ [SitUpCounter.swift:210] calculateTorsoAngle(from:deviceOrientation:): │    28   │    0.493 │    0.019 │   -0.048 │
默认	23:17:00.205343+0800	FitCount	ℹ️ [SitUpCounter.swift:210] calculateTorsoAngle(from:deviceOrientation:): │    29   │    0.511 │    0.063 │    0.151 │
默认	23:17:00.205430+0800	FitCount	ℹ️ [SitUpCounter.swift:210] calculateTorsoAngle(from:deviceOrientation:): │    29   │    0.511 │    0.063 │    0.151 │
默认	23:17:00.205594+0800	FitCount	ℹ️ [SitUpCounter.swift:210] calculateTorsoAngle(from:deviceOrientation:): │    30   │    0.520 │    0.050 │   -0.038 │
默认	23:17:00.205517+0800	FitCount	ℹ️ [SitUpCounter.swift:210] calculateTorsoAngle(from:deviceOrientation:): │    30   │    0.520 │    0.050 │   -0.038 │
默认	23:17:00.205755+0800	FitCount	ℹ️ [SitUpCounter.swift:210] calculateTorsoAngle(from:deviceOrientation:): │    31   │    0.643 │    0.052 │    0.131 │
默认	23:17:00.205678+0800	FitCount	ℹ️ [SitUpCounter.swift:210] calculateTorsoAngle(from:deviceOrientation:): │    31   │    0.643 │    0.052 │    0.131 │
默认	23:17:00.205950+0800	FitCount	ℹ️ [SitUpCounter.swift:210] calculateTorsoAngle(from:deviceOrientation:): │    32   │    0.664 │    0.059 │   -0.041 │
默认	23:17:00.205841+0800	FitCount	ℹ️ [SitUpCounter.swift:210] calculateTorsoAngle(from:deviceOrientation:): │    32   │    0.664 │    0.059 │   -0.041 │
默认	23:17:00.206048+0800	FitCount	ℹ️ [SitUpCounter.swift:214] calculateTorsoAngle(from:deviceOrientation:): └─────────┴──────────┴──────────┴──────────┘
默认	23:17:00.206156+0800	FitCount	ℹ️ [SitUpCounter.swift:214] calculateTorsoAngle(from:deviceOrientation:): └─────────┴──────────┴──────────┴──────────┘
默认	23:17:00.206345+0800	FitCount	ℹ️ [SitUpCounter.swift:215] calculateTorsoAngle(from:deviceOrientation:):
默认	23:17:00.206254+0800	FitCount	ℹ️ [SitUpCounter.swift:215] calculateTorsoAngle(from:deviceOrientation:):
默认	23:17:00.206513+0800	FitCount	ℹ️ [SitUpCounter.swift:216] calculateTorsoAngle(from:deviceOrientation:): worldLandmarks 数组信息打印完成
默认	23:17:00.206434+0800	FitCount	ℹ️ [SitUpCounter.swift:216] calculateTorsoAngle(from:deviceOrientation:): worldLandmarks 数组信息打印完成
默认	23:17:00.206597+0800	FitCount	ℹ️ [SitUpCounter.swift:217] calculateTorsoAngle(from:deviceOrientation:):
默认	23:17:00.206684+0800	FitCount	ℹ️ [SitUpCounter.swift:217] calculateTorsoAngle(from:deviceOrientation:):
默认	23:17:00.208359+0800	FitCount	ℹ️ [SitUpCounter.swift:315] calculateTorsoAngle(from:deviceOrientation:): calculateTorsoAngle  躯干角度计算详情（2D简化版）:
默认	23:17:00.208281+0800	FitCount	ℹ️ [SitUpCounter.swift:315] calculateTorsoAngle(from:deviceOrientation:): calculateTorsoAngle  躯干角度计算详情（2D简化版）:
默认	23:17:00.208437+0800	FitCount	ℹ️ [SitUpCounter.swift:316] calculateTorsoAngle(from:deviceOrientation:): calculateTorsoAngle  肩膀中点: (-0.507, -0.007)
默认	23:17:00.208514+0800	FitCount	ℹ️ [SitUpCounter.swift:316] calculateTorsoAngle(from:deviceOrientation:): calculateTorsoAngle  肩膀中点: (-0.507, -0.007)
默认	23:17:00.208671+0800	FitCount	ℹ️ [SitUpCounter.swift:317] calculateTorsoAngle(from:deviceOrientation:): calculateTorsoAngle  臀部中点: (-0.000, 0.000)
默认	23:17:00.208592+0800	FitCount	ℹ️ [SitUpCounter.swift:317] calculateTorsoAngle(from:deviceOrientation:): calculateTorsoAngle  臀部中点: (-0.000, 0.000)
默认	23:17:00.208837+0800	FitCount	ℹ️ [SitUpCounter.swift:318] calculateTorsoAngle(from:deviceOrientation:): calculateTorsoAngle  躯干向量: (-1.000, -0.015)
默认	23:17:00.208992+0800	FitCount	ℹ️ [SitUpCounter.swift:319] calculateTorsoAngle(from:deviceOrientation:): calculateTorsoAngle  重力方向: (-0.542, -0.026)
默认	23:17:00.208761+0800	FitCount	ℹ️ [SitUpCounter.swift:318] calculateTorsoAngle(from:deviceOrientation:): calculateTorsoAngle  躯干向量: (-1.000, -0.015)
默认	23:17:00.208916+0800	FitCount	ℹ️ [SitUpCounter.swift:319] calculateTorsoAngle(from:deviceOrientation:): calculateTorsoAngle  重力方向: (-0.542, -0.026)
默认	23:17:00.209496+0800	FitCount	ℹ️ [SitUpCounter.swift:321] calculateTorsoAngle(from:deviceOrientation:): calculateTorsoAngle  最终角度: 57.2°
默认	23:17:00.209227+0800	FitCount	ℹ️ [SitUpCounter.swift:321] calculateTorsoAngle(from:deviceOrientation:): calculateTorsoAngle  最终角度: 57.2°
